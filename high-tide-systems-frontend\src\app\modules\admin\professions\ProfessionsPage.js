"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Tag,
  Briefcase,
  CheckCircle,
  XCircle,
  Users,
  UserRound,
  Building,
  Layers
} from "lucide-react";
import ExportMenu from "@/components/ui/ExportMenu";
import { Protected } from "@/components/permissions/Protected";
import { professionsService } from "@/app/modules/admin/services/professionsService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import ProfessionFormModal from "@/components/admin/ProfessionFormModal";
import ProfessionGroupFormModal from "@/components/admin/ProfessionGroupFormModal";
import ProfessionUsersModal from "@/components/admin/ProfessionUsersModal";
import { ModuleHeader, ModuleInput, ModuleSelect, ModuleTable, ModalButton, ModuleTabs, MultiSelect, ModuleCheckbox } from "@/components/ui";
import { OccupationFilters } from "@/components/admin/OccupationFilters";
import { GroupsFilters } from "@/components/admin/GroupsFilters";

const ProfessionsPage = () => {
  const { user: currentUser } = useAuth();
  const { toast_success, toast_error } = useToast();
  const [professions, setProfessions] = useState([]);
  const [allProfessions, setAllProfessions] = useState([]); // Armazena todas as profissões para paginação manual
  const [groups, setGroups] = useState([]);
  const [allGroups, setAllGroups] = useState([]); // Armazena todos os grupos para paginação manual
  const [filteredGroups, setFilteredGroups] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingGroups, setIsLoadingGroups] = useState(true);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [search, setSearch] = useState("");
  const [groupFilter, setGroupFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [companyFilter, setCompanyFilter] = useState("");
  const [professionsFilter, setProfessionsFilter] = useState([]);
  const [professionOptions, setProfessionOptions] = useState([]);
  const [isLoadingProfessionOptions, setIsLoadingProfessionOptions] = useState(false);
  const [groupsFilter, setGroupsFilter] = useState([]);
  const [groupOptions, setGroupOptions] = useState([]);
  const [isLoadingGroupOptions, setIsLoadingGroupOptions] = useState(false);
  const [professionFormOpen, setProfessionFormOpen] = useState(false);
  const [groupFormOpen, setGroupFormOpen] = useState(false);
  const [usersModalOpen, setUsersModalOpen] = useState(false);
  const [selectedProfession, setSelectedProfession] = useState(null);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [activeTab, setActiveTab] = useState("professions"); // "professions" ou "groups"
  const [isExporting, setIsExporting] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  const [selectedGroupIds, setSelectedGroupIds] = useState([]);

  // Estados para o novo sistema de filtros
  const [occupationFilters, setOccupationFilters] = useState({
    search: "",
    companies: [],
    professionGroups: [],
    professions: [],
    status: ""
  });

  // Estados para paginação de profissões
  const [currentProfessionsPage, setCurrentProfessionsPage] = useState(1);
  const [totalProfessionsPages, setTotalProfessionsPages] = useState(1);
  const [totalProfessions, setTotalProfessions] = useState(0);

  // Estados para paginação de grupos
  const [currentGroupsPage, setCurrentGroupsPage] = useState(1);
  const [totalGroupsPages, setTotalGroupsPages] = useState(1);
  const [totalGroups, setTotalGroups] = useState(0);

  // Estado para controlar itens por página
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Funções para seleção múltipla
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedIds(professions.map(p => p.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectOne = (id, checked) => {
    setSelectedIds(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
  };

  // Funções para seleção múltipla de grupos
  const handleSelectAllGroups = (checked) => {
    if (checked) {
      setSelectedGroupIds(filteredGroups.map(g => g.id));
    } else {
      setSelectedGroupIds([]);
    }
  };

  const handleSelectOneGroup = (id, checked) => {
    setSelectedGroupIds(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
  };

  // Verificar se o usuário é administrador do sistema
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";

  // Função para carregar opções de profissões para o multi-select
  const loadProfessionOptions = useCallback(async () => {
    setIsLoadingProfessionOptions(true);
    try {
      // Carregar todas as profissões para o multi-select
      const data = await professionsService.getProfessions({
        active: true // Apenas profissões ativas por padrão
      });

      // Transformar os dados para o formato esperado pelo MultiSelect
      const options = data.map(profession => ({
        value: profession.id,
        label: profession.name
      }));

      setProfessionOptions(options);
    } catch (error) {
      console.error("Erro ao carregar opções de profissões:", error);
    } finally {
      setIsLoadingProfessionOptions(false);
    }
  }, []);

  // Função para carregar opções de grupos para o multi-select
  const loadGroupOptions = useCallback(async () => {
    setIsLoadingGroupOptions(true);
    try {
      // Carregar todos os grupos para o multi-select
      const data = await professionsService.getProfessionGroups({
        active: true // Apenas grupos ativos por padrão
      });

      // Transformar os dados para o formato esperado pelo MultiSelect
      const options = data.map(group => ({
        value: group.id,
        label: group.name
      }));

      setGroupOptions(options);
    } catch (error) {
      console.error("Erro ao carregar opções de grupos:", error);
    } finally {
      setIsLoadingGroupOptions(false);
    }
  }, []);

  // Carregar profissões
  const loadProfessions = async (
    page = currentProfessionsPage,
    searchQuery = search,
    groupId = groupFilter,
    status = statusFilter,
    company = companyFilter,
    professionIds = professionsFilter,
    sortField = "name",
    sortDirection = "asc",
    perPage = itemsPerPage
  ) => {
    setIsLoading(true);
    try {
      // Garantir que a página é um número
      const pageNumber = parseInt(page, 10);

      // Atualizar o estado da página atual
      setCurrentProfessionsPage(pageNumber);

      // Buscar todas as profissões
      const data = await professionsService.getProfessions({
        search: searchQuery || undefined,
        groupId: groupId || undefined,
        active: status === "" ? undefined : status === "active",
        companyId: company || undefined,
        professionIds: professionIds.length > 0 ? professionIds : undefined,
        sortField: sortField,
        sortDirection: sortDirection
      });

      // Armazenar todas as profissões para paginação manual
      setAllProfessions(data);

      // Calcular o total de itens e páginas
      const total = data.length;
      const pages = Math.ceil(total / perPage) || 1;

      // Aplicar paginação manual
      const startIndex = (pageNumber - 1) * perPage;
      const endIndex = startIndex + perPage;
      const paginatedProfessions = data.slice(startIndex, endIndex);

      // Atualizar o estado com os dados paginados manualmente
      setProfessions(paginatedProfessions);
      setTotalProfessions(total);
      setTotalProfessionsPages(pages);
    } catch (error) {
      console.error("Erro ao carregar profissões:", error);
      toast_error("Erro ao carregar profissões");
      setProfessions([]);
      setTotalProfessions(0);
      setTotalProfessionsPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para carregar grupos com ordenação
  const loadGroupsWithSort = async (sortField = 'name', sortDirection = 'asc', page = currentGroupsPage, perPage = itemsPerPage) => {
    setIsLoadingGroups(true);
    try {
      const pageNumber = parseInt(page, 10);
      setCurrentGroupsPage(pageNumber);

      const data = await professionsService.getProfessionGroups({
        search: search || undefined,
        active: statusFilter === "" ? undefined : statusFilter === "active",
        companyId: companyFilter || undefined,
        groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,
        sortField,
        sortDirection
      });

      setAllGroups(data);
      const total = data.length;
      const pages = Math.ceil(total / perPage) || 1;
      const startIndex = (pageNumber - 1) * perPage;
      const endIndex = startIndex + perPage;
      const paginatedGroups = data.slice(startIndex, endIndex);

      setFilteredGroups(paginatedGroups);
      setTotalGroups(total);
      setTotalGroupsPages(pages);
    } catch (error) {
      console.error("Erro ao carregar grupos com ordenação:", error);
      toast_error("Erro ao carregar grupos");
      setFilteredGroups([]);
      setTotalGroups(0);
      setTotalGroupsPages(1);
    } finally {
      setIsLoadingGroups(false);
    }
  };

  // Carregar grupos de profissões
  const loadGroups = async (page = currentGroupsPage, perPage = itemsPerPage) => {
    setIsLoadingGroups(true);
    try {
      // Garantir que a página é um número
      const pageNumber = parseInt(page, 10);

      // Atualizar o estado da página atual
      setCurrentGroupsPage(pageNumber);

      // Buscar todos os grupos
      const data = await professionsService.getProfessionGroups({
        active: activeTab === "professions" ? true : undefined // Na tab de profissões, só carrega os ativos
      });

      // Armazenar todos os grupos para paginação manual
      setAllGroups(data);

      if (activeTab === "professions") {
        // Na tab de profissões, não aplicamos paginação aos grupos (são usados apenas no filtro)
        setGroups(data);
      } else {
        // Na tab de grupos, aplicamos paginação
        // Calcular o total de itens e páginas
        const total = data.length;
        const pages = Math.ceil(total / perPage) || 1;

        // Aplicar paginação manual
        const startIndex = (pageNumber - 1) * perPage;
        const endIndex = startIndex + perPage;
        const paginatedGroups = data.slice(startIndex, endIndex);

        // Atualizar o estado com os dados paginados manualmente
        setGroups(data); // Mantemos todos os grupos para o filtro
        setFilteredGroups(paginatedGroups); // Apenas os 10 itens da página atual
        setTotalGroups(total);
        setTotalGroupsPages(pages);
      }
    } catch (error) {
      console.error("Erro ao carregar grupos de profissões:", error);
      toast_error("Erro ao carregar grupos de profissões");
      setGroups([]);
      setFilteredGroups([]);
      setTotalGroups(0);
      setTotalGroupsPages(1);
    } finally {
      setIsLoadingGroups(false);
    }
  };

  // Filtrar grupos quando o usuário submeter o formulário
  const filterGroups = (
    searchTerm,
    page = currentGroupsPage,
    groupIds = groupsFilter,
    sortField = 'name',
    sortDirection = 'asc',
    perPage = itemsPerPage
  ) => {
    const pageNumber = parseInt(page, 10);
    setCurrentGroupsPage(pageNumber);



    const loadFilteredGroups = async () => {
      setIsLoadingGroups(true);
      try {
        const data = await professionsService.getProfessionGroups({
          search: searchTerm || undefined,
          active: statusFilter === "" ? undefined : statusFilter === "active",
          companyId: companyFilter || undefined,
          groupIds: groupIds.length > 0 ? groupIds : undefined,
          sortField,
          sortDirection
        });

        setAllGroups(data);
        const total = data.length;
        const pages = Math.ceil(total / perPage) || 1;
        const startIndex = (pageNumber - 1) * perPage;
        const endIndex = startIndex + perPage;
        const paginatedGroups = data.slice(startIndex, endIndex);

        setFilteredGroups(paginatedGroups);
        setTotalGroups(total);
        setTotalGroupsPages(pages);
      } catch (error) {
        console.error("Erro ao filtrar grupos:", error);
        toast_error("Erro ao filtrar grupos");
        setFilteredGroups([]);
        setTotalGroups(0);
        setTotalGroupsPages(1);
      } finally {
        setIsLoadingGroups(false);
      }
    };

    loadFilteredGroups();
  };

  // Carregar empresas (apenas para system admin)
  const loadCompanies = async () => {
    if (!isSystemAdmin) return;

    setIsLoadingCompanies(true);
    try {
      const companies = await companyService.getCompaniesForSelect();
      setCompanies(companies || []);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  useEffect(() => {
    loadProfessions();
    loadGroups();
    if (isSystemAdmin) {
      loadCompanies();
    }
    // Carregar opções para os multi-selects
    loadProfessionOptions();
    loadGroupOptions();
  }, [isSystemAdmin, loadProfessionOptions, loadGroupOptions]);

  // Recarregar dados quando a tab mudar
  useEffect(() => {
    setSearch(""); // Limpar a busca ao trocar de tab
    setGroupFilter("");
    setStatusFilter("");
    setCompanyFilter("");
    setProfessionsFilter([]);
    setGroupsFilter([]);

    // Resetar os filtros do novo sistema
    if (activeTab === "professions") {
      setOccupationFilters({
        search: "",
        companies: [],
        professionGroups: [],
        professions: [],
        status: ""
      });
    }

    // Resetar para a primeira página
    if (activeTab === "professions") {
      setCurrentProfessionsPage(1);
      loadProfessions(1);
    } else {
      setCurrentGroupsPage(1);
      loadGroups(1); // Recarregar grupos com filtro diferente dependendo da tab
    }
  }, [activeTab]);

  // Função de busca removida, agora usamos estados locais nos componentes de filtro

  const handleGroupFilterChange = (value) => {
    setGroupFilter(value);
    // Acionar a busca automaticamente quando o usuário selecionar um grupo
    setCurrentProfessionsPage(1);
    loadProfessions(1, search, value, statusFilter, companyFilter, professionsFilter);
  };

  const handleStatusFilterChange = (value) => {
    setStatusFilter(value);
    // Acionar a busca automaticamente quando o usuário selecionar um status
    if (activeTab === "professions") {
      setCurrentProfessionsPage(1);
      loadProfessions(1, search, groupFilter, value, companyFilter, professionsFilter);
    } else {
      setCurrentGroupsPage(1);
      filterGroups(search, 1, groupsFilter);
    }
  };

  const handleCompanyFilterChange = (value) => {
    setCompanyFilter(value);
    // Acionar a busca automaticamente quando o usuário selecionar uma empresa
    if (activeTab === "professions") {
      setCurrentProfessionsPage(1);
      loadProfessions(1, search, groupFilter, statusFilter, value, professionsFilter);
    } else {
      setCurrentGroupsPage(1);
      filterGroups(search, 1, groupsFilter);
    }
  };

  const handleProfessionsFilterChange = (value) => {
    setProfessionsFilter(value);
    // Acionar a busca automaticamente quando o usuário selecionar ou remover uma profissão
    setCurrentProfessionsPage(1);
    loadProfessions(1, search, groupFilter, statusFilter, companyFilter, value);
  };

  const handleGroupsFilterChange = (value) => {
    setGroupsFilter(value);
    // Acionar a busca automaticamente quando o usuário selecionar ou remover um grupo
    setCurrentGroupsPage(1);
    filterGroups(search, 1, value);
  };

  // Funções para o novo sistema de filtros
  const handleOccupationFiltersChange = (newFilters) => {
    setOccupationFilters(newFilters);
  };

  const handleOccupationSearch = (filters) => {
    setCurrentProfessionsPage(1);
    // Converter os filtros para o formato esperado pela API
    const searchQuery = filters.search || "";
    const groupIds = filters.professionGroups || [];
    const professionIds = filters.professions || [];
    const companyIds = filters.companies || [];
    const status = filters.status || "";

    // Mapear os filtros para os filtros existentes
    setSearch(searchQuery);
    setGroupsFilter(groupIds.length > 0 ? groupIds[0] : "");
    setProfessionsFilter(professionIds);
    setCompanyFilter(companyIds.length > 0 ? companyIds[0] : "");
    setStatusFilter(status);

    // Carregar profissões com os novos filtros
    loadProfessions(1, searchQuery, groupIds.length > 0 ? groupIds[0] : "", status, companyIds.length > 0 ? companyIds[0] : "", professionIds);
  };

  const handleOccupationClearFilters = () => {
    const clearedFilters = {
      search: "",
      companies: [],
      professionGroups: [],
      professions: [],
      status: ""
    };

    setOccupationFilters(clearedFilters);
    setSearch("");
    setGroupFilter("");
    setStatusFilter("");
    setCompanyFilter("");
    setProfessionsFilter([]);
    setGroupsFilter([]);
    setCurrentProfessionsPage(1);
    loadProfessions(1, "", "", "", "", []);
  };

  const handleResetFilters = () => {
    setSearch("");
    setGroupFilter("");
    setStatusFilter("");
    setCompanyFilter("");
    setProfessionsFilter([]);
    setGroupsFilter([]);

    // Resetar para a primeira página
    if (activeTab === "professions") {
      setCurrentProfessionsPage(1);
      loadProfessions(1, "", "", "", "", []);
    } else {
      setCurrentGroupsPage(1);
      filterGroups("", 1, []); // Chamada direta para resetar os filtros
    }

    // Forçar a re-renderização dos componentes de filtro
    setTimeout(() => {
      const event = new Event('reset');
      document.dispatchEvent(event);
    }, 0);
  };

  // Função para lidar com a mudança de página de profissões
  const handleProfessionsPageChange = (page) => {
    loadProfessions(page, search, groupFilter, statusFilter, companyFilter, professionsFilter);
  };

  // Função para lidar com a mudança de página de grupos
  const handleGroupsPageChange = (page) => {
    filterGroups(search, page, groupsFilter); // Chamada direta para mudança de página
  };

  const handleEditProfession = (profession) => {
    setSelectedProfession(profession);
    setProfessionFormOpen(true);
  };

  const handleEditGroup = (group) => {
    setSelectedGroup(group); // Se group for null, será criação de novo grupo
    setGroupFormOpen(true);
  };

  const handleDeleteProfession = (profession) => {
    setSelectedProfession(profession);
    setActionToConfirm({
      type: "delete-profession",
      message: `Excluir permanentemente a profissão ${profession.name}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleViewUsers = (profession) => {
    setSelectedProfession(profession);
    setUsersModalOpen(true);
  };

  const handleDeleteGroup = (group) => {
    setSelectedGroup(group);
    setActionToConfirm({
      type: "delete-group",
      message: `Excluir permanentemente o grupo ${group.name}?`,
    });
    setConfirmationDialogOpen(true);
  };

  // Função para exportar profissões
  const handleExportProfessions = async (format) => {
    setIsExporting(true);
    try {
      // Exportar usando os mesmos filtros da tabela atual
      await professionsService.exportProfessions({
        search: search || undefined,
        professionIds: professionsFilter.length > 0 ? professionsFilter : undefined,
        groupId: groupFilter || undefined,
        active: statusFilter === "" ? undefined : statusFilter === "active",
        companyId: companyFilter || undefined
      }, format);
    } catch (error) {
      console.error("Erro ao exportar profissões:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Função para exportar grupos de profissões
  const handleExportGroups = async (format) => {
    setIsExporting(true);
    try {
      // Exportar usando os mesmos filtros da tabela atual
      await professionsService.exportProfessionGroups({
        search: search || undefined,
        groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,
        active: statusFilter === "" ? undefined : statusFilter === "active",
        companyId: companyFilter || undefined
      }, format);
    } catch (error) {
      console.error("Erro ao exportar grupos de profissões:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const confirmAction = async () => {
    try {
      if (actionToConfirm.type === "delete-profession") {
        await professionsService.deleteProfession(selectedProfession.id);
        toast_success("Profissão excluída com sucesso");
        loadProfessions();
      } else if (actionToConfirm.type === "delete-group") {
        await professionsService.deleteProfessionGroup(selectedGroup.id);
        toast_success("Grupo excluído com sucesso");
        setSelectedGroup(null); // Limpar o grupo selecionado após exclusão
        loadGroups();
        loadProfessions(); // Recarregar profissões para atualizar os grupos
      }
    } catch (error) {
      console.error("Erro ao executar ação:", error);
      toast_error(error.response?.data?.message || "Erro ao executar ação");
    } finally {
      setConfirmationDialogOpen(false);
    }
  };

  // Configuração das tabs para o ModuleTabs
  const tabsConfig = [
    {
      id: "professions",
      label: "Profissões",
      icon: <Briefcase size={18} />,
      permission: "admin.professions.view"
    },
    {
      id: "groups",
      label: "Grupos de Profissões",
      icon: <Layers size={18} />,
      permission: "admin.profession-groups.view"
    }
  ];

  // Filtrar tabs com base nas permissões do usuário
  const filteredTabs = tabsConfig.filter(tab => {
    if (!tab.permission) return true;
    return <Protected permission={tab.permission} showFallback={false}>
      {true}
    </Protected>;
  });

  // Componente de filtros para profissões
  const ProfessionsFilters = () => {
    // Estado local para o campo de busca
    const [localSearch, setLocalSearch] = useState(search);

    // Atualizar o estado local quando o estado global mudar
    useEffect(() => {
      setLocalSearch(search);
    }, [search]);

    // Ouvir o evento de reset
    useEffect(() => {
      const handleReset = () => {
        setLocalSearch("");
      };
      document.addEventListener('reset', handleReset);
      return () => {
        document.removeEventListener('reset', handleReset);
      };
    }, []);

    // Função para atualizar o estado local sem afetar o estado global
    const handleLocalSearchChange = (e) => {
      setLocalSearch(e.target.value);
    };

    // Função para aplicar o filtro quando o botão for clicado
    const applyFilter = () => {
      setSearch(localSearch);
      setCurrentProfessionsPage(1);

      // Log para debug
      console.log("Aplicando filtros de profissões:", {
        search: localSearch,
        groupFilter,
        statusFilter,
        companyFilter,
        professionsFilter
      });

      loadProfessions(1, localSearch, groupFilter, statusFilter, companyFilter, professionsFilter);
    };

    return (
      <div className="flex flex-col gap-4 mt-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 h-5 w-5" />
            <input
              type="text"
              placeholder="Buscar por nome ou descrição..."
              value={localSearch}
              onChange={handleLocalSearchChange}
              className="w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  applyFilter();
                }
              }}
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <ModuleSelect
              moduleColor="admin"
              value={groupFilter}
              onChange={(e) => handleGroupFilterChange(e.target.value)}
              disabled={isLoadingGroups}
            >
              <option value="">Todos os grupos</option>
              <option value="null">Sem grupo</option>
              {groups.map((group) => (
                <option key={group.id} value={group.id}>
                  {group.name}
                </option>
              ))}
            </ModuleSelect>

            <ModuleSelect
              moduleColor="admin"
              value={statusFilter}
              onChange={(e) => handleStatusFilterChange(e.target.value)}
            >
              <option value="">Todos os status</option>
              <option value="active">Ativas</option>
              <option value="inactive">Inativas</option>
            </ModuleSelect>

            {/* Filtro de empresa (apenas para system admin) */}
            {isSystemAdmin && (
              <ModuleSelect
                moduleColor="admin"
                value={companyFilter}
                onChange={(e) => handleCompanyFilterChange(e.target.value)}
                disabled={isLoadingCompanies}
              >
                <option value="">Todas as empresas</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            )}

            <ModalButton
              moduleColor="admin"
              type="button"
              onClick={applyFilter}
            >
              <Filter size={16} className="sm:hidden" />
              <span className="hidden sm:inline">Filtrar</span>
            </ModalButton>

            <ModalButton
              moduleColor="admin"
              type="button"
              onClick={handleResetFilters}
              variant="secondary"
            >
              <RefreshCw size={16} className="sm:hidden" />
              <span className="hidden sm:inline">Limpar</span>
            </ModalButton>
          </div>
        </div>

        {/* Multi-select para filtrar por múltiplas profissões */}
        <div className="w-full">
          <MultiSelect
            label="Filtrar por Profissões"
            value={professionsFilter}
            onChange={handleProfessionsFilterChange}
            options={professionOptions}
            placeholder="Selecione uma ou mais profissões pelo nome..."
            loading={isLoadingProfessionOptions}
            moduleOverride="admin"
          />
        </div>
      </div>
    );
  };

  // Estados para o novo sistema de filtros de grupos
  const [groupFilters, setGroupFilters] = useState({
    search: "",
    companies: [],
    status: "",
    groups: []
  });

  const handleGroupFiltersChange = (newFilters) => {
    setGroupFilters(newFilters);
  };

  const handleGroupSearch = (filters) => {
    setCurrentGroupsPage(1);
    const searchQuery = filters.search || "";
    const companyIds = filters.companies || [];
    const status = filters.status || "";
    const groupIds = filters.groups || [];

    setSearch(searchQuery);
    setCompanyFilter(companyIds.length > 0 ? companyIds[0] : "");
    setStatusFilter(status);
    setGroupsFilter(groupIds);

    filterGroups(searchQuery, 1, groupIds);
  };

  // Import tutorial steps from tutorialMapping
  const professionsGroupsTutorialSteps = useMemo(() => {
    // Import dynamically to avoid circular dependencies
    const tutorialMap = require('@/tutorials/tutorialMapping').default;
    return tutorialMap['/dashboard/admin/professions'] || [];
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          {activeTab === "professions" ?
            <Briefcase size={24} className="mr-2 text-slate-600 dark:text-slate-400" /> :
            <Layers size={24} className="mr-2 text-slate-600 dark:text-slate-400" />}
          {activeTab === "professions" ? "Profissões" : "Grupos de Profissões"}
        </h1>

        <div className="flex items-center gap-2">
          {/* Botão de exclusão em massa - apenas para profissões */}
          {activeTab === "professions" && selectedIds.length > 0 && (
            <button
              onClick={() => console.log('Excluir selecionados:', selectedIds)}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg"
              title="Excluir selecionados"
            >
              <Trash size={18} />
              <span className="font-medium">Excluir Selecionados ({selectedIds.length})</span>
            </button>
          )}
          {/* Botão de exportar */}
          {activeTab === "professions" && (
            <ExportMenu
              onExport={handleExportProfessions}
              isExporting={isExporting}
              disabled={isLoading || professions.length === 0}
              className="text-slate-700 dark:text-slate-300"
            />
          )}
          {/* Botão de exclusão em massa - apenas para grupos */}
          {activeTab === "groups" && selectedGroupIds.length > 0 && (
            <button
              onClick={() => {
                // TODO: Implementar exclusão em massa de grupos
              }}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg"
              title="Excluir selecionados"
            >
              <Trash size={18} />
              <span className="font-medium">Excluir Selecionados ({selectedGroupIds.length})</span>
            </button>
          )}
          {activeTab === "groups" && (
            <ExportMenu
              onExport={handleExportGroups}
              isExporting={isExporting}
              disabled={isLoadingGroups || filteredGroups.length === 0}
              className="text-slate-700 dark:text-slate-300"
            />
          )}

          {/* Botão de adicionar */}
          {activeTab === "professions" && (
            <button
              onClick={() => {
                setSelectedProfession(null);
                setProfessionFormOpen(true);
              }}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg"
            >
              <Plus size={18} />
              <span className="font-medium">Nova Profissão</span>
            </button>
          )}
          {activeTab === "groups" && (
            <button
              onClick={() => {
                setSelectedGroup(null);
                setGroupFormOpen(true);
              }}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg"
            >
              <Plus size={18} />
              <span className="font-medium">Novo Grupo</span>
            </button>
          )}
        </div>
      </div>

      <ModuleHeader
        title="Filtros e Busca"
        icon={<Filter size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
        moduleColor="admin"
        tutorialSteps={professionsGroupsTutorialSteps}
        tutorialName="admin-professions-groups-overview"
        filters={
          <>
            <ModuleTabs
              tabs={filteredTabs}
              activeTab={activeTab}
              onTabChange={setActiveTab}
              moduleColor="admin"
            />
            <div className="mt-6">
              {activeTab === "professions" ? (
                <OccupationFilters
                  filters={occupationFilters}
                  onFiltersChange={handleOccupationFiltersChange}
                  onSearch={handleOccupationSearch}
                  onClearFilters={handleOccupationClearFilters}
                />
              ) : (
                <GroupsFilters
                  filters={groupFilters}
                  onFiltersChange={handleGroupFiltersChange}
                  onSearch={handleGroupSearch}
                />
              )}
            </div>
          </>
        }
      />

      {/* Tabela de Profissões */}
      {activeTab === "professions" && (
        <Protected permission="admin.professions.view">
          <ModuleTable
            moduleColor="admin"
            title="Lista de Profissões"
            headerContent={
              <div className="flex items-center gap-2">
                <button
                  onClick={() => loadProfessions()}
                  className="p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md"
                  title="Atualizar lista"
                >
                  <RefreshCw size={18} />
                </button>
              </div>
            }
            columns={[
              { header: '', field: 'select', width: '50px', sortable: false },
              { header: 'Profissão', field: 'name', width: '20%' },
              { header: 'Grupo', field: 'group', width: '15%' },
              ...(isSystemAdmin ? [{ header: 'Empresa', field: 'company', width: '15%' }] : []),
              { header: 'Descrição', field: 'description', width: '20%' },
              { header: 'Usuários', field: 'users', width: '10%' },
              { header: 'Status', field: 'active', width: '10%' },
              { header: 'Ações', field: 'actions', className: 'text-right', width: '10%', sortable: false }
            ]}
            data={professions}
            isLoading={isLoading}
            emptyMessage="Nenhuma profissão encontrada"
            emptyIcon={<Briefcase size={24} />}
            tableId="admin-professions-table"
            enableColumnToggle={true}
            defaultSortField="name"
            defaultSortDirection="asc"
            currentPage={currentProfessionsPage}
            totalPages={totalProfessionsPages}
            totalItems={totalProfessions}
            onPageChange={handleProfessionsPageChange}
            onSort={(field, direction) => {
              // Quando a ordenação mudar, recarregar as profissões com os novos parâmetros de ordenação
              loadProfessions(
                currentProfessionsPage,
                search,
                groupFilter,
                statusFilter,
                companyFilter,
                professionsFilter,
                field,
                direction
              );
            }}
            showPagination={true}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              loadProfessions(1, search, groupFilter, statusFilter, companyFilter, professionsFilter, "name", "asc", newItemsPerPage);
            }}
            selectedIds={selectedGroupIds}
            onSelectAll={handleSelectAllGroups}
            renderRow={(profession, index, moduleColors, visibleColumns) => (
              <tr key={profession.id} className={moduleColors.hoverBg}>
                {visibleColumns.includes('select') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <ModuleCheckbox
                      moduleColor="admin"
                      checked={selectedIds.includes(profession.id)}
                      onChange={(e) => handleSelectOne(profession.id, e.target.checked)}
                      name={`select-profession-${profession.id}`}
                    />
                  </td>
                )}
                {visibleColumns.includes('name') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center">
                        <Briefcase size={20} />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                          {profession.name}
                        </div>
                      </div>
                    </div>
                  </td>
                )}

                {visibleColumns.includes('group') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    {profession.group ? (
                      <span className="px-2 py-1 text-xs rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400">
                        {profession.group.name}
                      </span>
                    ) : (
                      <span className="text-neutral-400 dark:text-neutral-500 italic">
                        Sem grupo
                      </span>
                    )}
                  </td>
                )}

                {isSystemAdmin && visibleColumns.includes('company') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    {profession.company ? (
                      <div className="flex items-center gap-1">
                        <Building size={14} className="text-neutral-500" />
                        <span className="text-sm text-neutral-600 dark:text-neutral-300">
                          {profession.company.name}
                        </span>
                      </div>
                    ) : (
                      <span className="text-neutral-400 dark:text-neutral-500 italic">
                        Sem empresa
                      </span>
                    )}
                  </td>
                )}

                {visibleColumns.includes('description') && (
                  <td className="px-6 py-4">
                    <div className="text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2">
                      {profession.description || (
                        <span className="text-neutral-400 dark:text-neutral-500 italic">
                          Sem descrição
                        </span>
                      )}
                    </div>
                  </td>
                )}

                {visibleColumns.includes('users') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Users size={16} className="text-neutral-400 dark:text-neutral-500 mr-2" />
                      <span className="text-sm text-neutral-600 dark:text-neutral-300">
                        {profession._count?.users || 0} usuários
                      </span>
                    </div>
                  </td>
                )}

                {visibleColumns.includes('active') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${profession.active
                          ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                          : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                        }`}
                    >
                      {profession.active ? (
                        <>
                          <CheckCircle size={12} />
                          <span>Ativa</span>
                        </>
                      ) : (
                        <>
                          <XCircle size={12} />
                          <span>Inativa</span>
                        </>
                      )}
                    </span>
                  </td>
                )}

                {visibleColumns.includes('actions') && (
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end gap-2">
                      <button
                        onClick={() => handleViewUsers(profession)}
                        className="p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md"
                        title="Ver usuários com esta profissão"
                      >
                        <UserRound size={18} />
                      </button>
                      <Protected permission="admin.professions.edit">
                        <button
                          onClick={() => handleEditProfession(profession)}
                          className="p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md"
                          title="Editar profissão"
                        >
                          <Edit size={18} />
                        </button>
                      </Protected>
                      <Protected permission="admin.professions.delete">
                        <button
                          onClick={() => handleDeleteProfession(profession)}
                          className="p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md"
                          title="Excluir profissão"
                          disabled={profession._count?.users > 0}
                        >
                          <Trash size={18} className={profession._count?.users > 0 ? "opacity-50 cursor-not-allowed" : ""} />
                        </button>
                      </Protected>
                    </div>
                  </td>
                )}
              </tr>
            )}
          />
        </Protected>
      )}

      {/* Tabela de Grupos de Profissões */}
      {activeTab === "groups" && (
        <Protected permission="admin.profession-groups.view">
          <ModuleTable
            moduleColor="admin"
            title="Lista de Grupos"
            headerContent={
              <button
                onClick={() => loadGroups()}
                className="p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md"
                title="Atualizar lista"
              >
                <RefreshCw size={18} />
              </button>
            }
            columns={[
              { header: '', field: 'select', width: '50px', sortable: false },
              { header: 'Grupo', field: 'name', width: '20%' },
              { header: 'Descrição', field: 'description', width: '25%' },
              ...(isSystemAdmin ? [{ header: 'Empresa', field: 'company', width: '15%' }] : []),
              { header: 'Profissões', field: 'professions', width: '15%' },
              { header: 'Status', field: 'active', width: '10%' },
              { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }
            ]}
            data={filteredGroups}
            isLoading={isLoadingGroups}
            emptyMessage="Nenhum grupo encontrado"
            emptyIcon={<Tag size={24} />}
            tableId="admin-profession-groups-table"
            enableColumnToggle={true}
            defaultSortField="name"
            defaultSortDirection="asc"
            currentPage={currentGroupsPage}
            totalPages={totalGroupsPages}
            totalItems={totalGroups}
            onPageChange={handleGroupsPageChange}
            onSort={(field, direction) => {
              // Quando a ordenação mudar, recarregar os grupos com os novos parâmetros de ordenação
              loadGroupsWithSort(field, direction);
            }}
            showPagination={true}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              filterGroups(search, 1, groupsFilter, newItemsPerPage);
            }}
            renderRow={(group, index, moduleColors, visibleColumns) => (
              <tr key={group.id} className={moduleColors.hoverBg}>
                {visibleColumns.includes('select') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <ModuleCheckbox
                      moduleColor="admin"
                      checked={selectedGroupIds.includes(group.id)}
                      onChange={(e) => handleSelectOneGroup(group.id, e.target.checked)}
                      name={`select-group-${group.id}`}
                    />
                  </td>
                )}
                {visibleColumns.includes('name') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center">
                        <Tag size={20} />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                          {group.name}
                        </div>
                      </div>
                    </div>
                  </td>
                )}

                {visibleColumns.includes('description') && (
                  <td className="px-6 py-4">
                    <div className="text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2">
                      {group.description || (
                        <span className="text-neutral-400 dark:text-neutral-500 italic">
                          Sem descrição
                        </span>
                      )}
                    </div>
                  </td>
                )}

                {isSystemAdmin && visibleColumns.includes('company') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    {group.company ? (
                      <div className="flex items-center gap-1">
                        <Building size={14} className="text-neutral-500" />
                        <span className="text-sm text-neutral-600 dark:text-neutral-300">
                          {group.company.name}
                        </span>
                      </div>
                    ) : (
                      <span className="text-neutral-400 dark:text-neutral-500 italic">
                        Sem empresa
                      </span>
                    )}
                  </td>
                )}

                {visibleColumns.includes('professions') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Briefcase size={16} className="text-neutral-400 dark:text-neutral-500 mr-2" />
                      <span className="text-sm text-neutral-600 dark:text-neutral-300">
                        {group._count?.professions || 0} profissões
                      </span>
                    </div>
                  </td>
                )}

                {visibleColumns.includes('active') && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${group.active
                          ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                          : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                        }`}
                    >
                      {group.active ? (
                        <>
                          <CheckCircle size={12} />
                          <span>Ativo</span>
                        </>
                      ) : (
                        <>
                          <XCircle size={12} />
                          <span>Inativo</span>
                        </>
                      )}
                    </span>
                  </td>
                )}

                {visibleColumns.includes('actions') && (
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end gap-2">
                      <Protected permission="admin.profession-groups.edit">
                        <button
                          onClick={() => handleEditGroup(group)}
                          className="p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md"
                          title="Editar grupo"
                        >
                          <Edit size={18} />
                        </button>
                      </Protected>
                      <Protected permission="admin.profession-groups.delete">
                        <button
                          onClick={() => handleDeleteGroup(group)}
                          className="p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md"
                          title="Excluir grupo"
                          disabled={group._count?.professions > 0}
                        >
                          <Trash size={18} className={group._count?.professions > 0 ? "opacity-50 cursor-not-allowed" : ""} />
                        </button>
                      </Protected>
                    </div>
                  </td>
                )}
              </tr>
            )}
          />
        </Protected>
      )}

      {/* Modais */}
      <ProfessionFormModal
        isOpen={professionFormOpen}
        onClose={() => setProfessionFormOpen(false)}
        profession={selectedProfession}
        groups={groups}
        onSuccess={() => {
          setProfessionFormOpen(false);
          loadProfessions();
        }}
      />

      <ProfessionGroupFormModal
        isOpen={groupFormOpen}
        onClose={() => setGroupFormOpen(false)}
        group={selectedGroup}
        onSuccess={async () => {
          setGroupFormOpen(false);

          // Se havia um grupo selecionado, recarregar os dados atualizados dele primeiro
          if (selectedGroup) {
            try {
              const updatedGroup = await professionsService.getProfessionGroupById(selectedGroup.id);
              setSelectedGroup(updatedGroup);
            } catch (error) {
              console.error("Erro ao recarregar dados do grupo:", error);
            }
          }

          // Recarregar grupos
          await loadGroups();

          // Recarregar profissões para atualizar os grupos
          loadProfessions();
        }}
      />

      <ProfessionUsersModal
        isOpen={usersModalOpen}
        onClose={() => setUsersModalOpen(false)}
        professionId={selectedProfession?.id}
      />

      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
        variant={actionToConfirm?.type.includes("delete") ? "danger" : "primary"}
        moduleColor="admin"
        confirmText={actionToConfirm?.type.includes("delete") ? "Excluir" : "Confirmar"}
      />

      {/* Botão flutuante removido, mantendo apenas o botão de ajuda/tutorial que é adicionado pelo ModuleHeader */}
    </div>
  );
};

export default ProfessionsPage;
